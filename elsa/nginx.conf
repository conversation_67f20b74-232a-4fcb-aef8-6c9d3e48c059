worker_processes  1;
error_log  /usr/local/etc/nginx/logs/error.log  debug;

events {
    worker_connections  1024;
}


http {
    resolver ***************;
    include       mime.types;
    default_type  application/octet-stream;
    client_max_body_size 100m;
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for" "$request_uri" "$proxy_host"'
                      '$upstream_http_status $upstream_response_time';

    proxy_headers_hash_max_size 1024;
    proxy_headers_hash_bucket_size 128;

    sendfile        on;
    keepalive_timeout  65;

    map $http_x_grpc_web $use_grpc_location {
        default 0;
        "~." 1;  # 当 X-Grpc-Web 请求头存在时，设置为 1
    }

    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    server {
        listen 3000;
        server_name _;  # 匹配所有域名[3](@ref)

        location / {
            proxy_pass http://localhost:13001;  # 原Vite开发服务器地址
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /authConfig {
            proxy_pass https://bs-test-llm.ai4c.cn/authConfig;
            
            # 注入 CORS 头
            add_header 'Access-Control-Allow-Origin' 'http://localhost:3000' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization' always;
            
            # 处理预检请求
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Max-Age' 1728000;
                return 204;
            }
        }

        # 全局代理配置
        location /elsa {
            proxy_pass http://localhost:13000/elsa;
            proxy_set_header Authorization $http_authorization;
        }

        location /_content {
            proxy_pass http://localhost:13000/_content;
            # 移除硬编码的 Authorization 头（由客户端携带）
            proxy_set_header Authorization $http_authorization;
        }

        location /_framework {
            proxy_pass http://localhost:13000/_framework;
            # 移除硬编码的 Authorization 头（由客户端携带）
            proxy_set_header Authorization $http_authorization;
        }

        location /workflows {
            proxy_pass http://localhost:13000/workflows;
            # 移除硬编码的 Authorization 头（由客户端携带）
            proxy_set_header Authorization $http_authorization;
        }

        # 精准拦截CSS请求（网页3路径匹配）
        location = /_content/MudBlazor/MudBlazor.min.css {
            alias /usr/local/etc/nginx/custom_styles/mudblazor.min.css; # 网页7路径规范
            add_header Content-Type "text/css";
            access_log off;
            # expires 7d;
            add_header Expires "0";
        }

        location /_content/Elsa.Studio.Shell/css/shell.css {
            alias /usr/local/etc/nginx/custom_styles/shell.css;
            add_header Content-Type "text/css";
            access_log off;
            # expires 7d;
            add_header Expires "0";
        }

        location /_content/Radzen.Blazor/css/material-base.css {
            alias /usr/local/etc/nginx/custom_styles/material-base.css;
            add_header Content-Type "text/css";
            access_log off;
            # expires 7d;
            add_header Expires "0";
        }
    }


    include servers/*;
}
