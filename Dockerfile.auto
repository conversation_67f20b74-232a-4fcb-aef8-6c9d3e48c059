ARG VERSION=18.20.4
FROM ai4c-tcr.tencentcloudcr.com/infra/node:${VERSION} AS builder

WORKDIR /app

# 安装依赖
COPY package.json ./
RUN npm install --legacy-peer-deps

# 复制项目代码
COPY . .

# 构建项目
RUN npm run build

# 使用nginx作为生产环境服务器
FROM ai4c-tcr.tencentcloudcr.com/infra/nginx:stable-alpine

# 复制构建产物到nginx服务目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 创建nginx日志目录
RUN mkdir -p /usr/local/etc/nginx/logs

# 复制nginx配置模板
COPY devops/nginx-auto.conf /etc/nginx/nginx.conf.template

# 设置默认环境变量
ENV API_BASE_URL=http://api.zyagi.cn:90/af
ENV ELSA_BASE_URL=http://api.zyagi.cn:90/af

# 暴露3000端口
EXPOSE 80

# 修改启动脚本，添加envsubst支持
RUN echo '#!/bin/sh' > /docker-entrypoint.sh \
    && echo 'echo "API_BASE_URL: $API_BASE_URL"' >> /docker-entrypoint.sh \
    && echo '# 提取域名，兼容http和https' >> /docker-entrypoint.sh \
    && echo 'API_HOST=$(echo $API_BASE_URL | sed -E "s|^https?://([^/]+).*|\1|")' >> /docker-entrypoint.sh \
    && echo 'export API_HOST' >> /docker-entrypoint.sh \
    && echo 'echo "API_HOST: $API_HOST"' >> /docker-entrypoint.sh \
    && echo 'envsubst "\$API_BASE_URL \$API_HOST" < /etc/nginx/nginx.conf.template > /etc/nginx/nginx.conf' >> /docker-entrypoint.sh \
    && echo 'nginx -g "daemon off;"' >> /docker-entrypoint.sh \
    && chmod +x /docker-entrypoint.sh

# 设置启动命令
CMD ["/docker-entrypoint.sh"] 