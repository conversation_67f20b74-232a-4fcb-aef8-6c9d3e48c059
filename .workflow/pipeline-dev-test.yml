version: '1.0'
name: pipeline-dev-test
displayName: pipeline-dev-test
triggers:
  trigger: auto
  push:
    branches:
      precise:
        - dev

stages:
  - name: stage-get-timestamp
    displayName: 获取时间戳（北京时间）
    strategy: naturally
    trigger: auto
    executor:
      - weyhd_geffzhang
      - qiu-tianfu
      - verdure-hiro
      - bwlcSpace
      - dyy2426
      - bigLoin
    steps:
      - step: execute@docker
        name: get_timestamp
        displayName: 获取当前北京时间戳
        certificate: 95a6bdd0-33ca-013e-25df-66d80465206f
        image: ai4c-tcr.tencentcloudcr.com/temp/alpine:latest
        command:
          - echo "替换 apk 源为阿里云..."
          - echo "http://mirrors.aliyun.com/alpine/v3.22/main/" > /etc/apk/repositories
          - echo "http://mirrors.aliyun.com/alpine/v3.22/community/" >> /etc/apk/repositories
          - apk update
          - apk add --no-cache tzdata
          - cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
          - echo "Asia/Shanghai" > /etc/timezone
          - TIMESTAMP=$(date "+%Y%m%d%H%M")
          - echo "当前北京时间戳：$TIMESTAMP"
          - echo "TIMESTAMP_ENV=$TIMESTAMP" > timestamp.env
        artifacts:
          - name: TIMESTAMP_ENV
            path:
              - ./timestamp.env

  - name: stage-build-image
    displayName: 镜像构建
    strategy: naturally
    trigger: auto
    executor:
      - weyhd_geffzhang
      - qiu-tianfu
      - verdure-hiro
      - bwlcSpace
      - dyy2426
      - bigLoin
    steps:
      - step: build@docker
        name: build_docker_image
        displayName: 构建 agentfoundryui 镜像
        type: cert
        certificate: 95a6bdd0-33ca-013e-25df-66d80465206f
        dockerfile: ./Dockerfile.ai4c
        context: ./output
        artifacts:
          - TIMESTAMP_ENV
        isCache: false
        beforeScript:
          - echo "导入时间戳环境变量..."
          - source ./output/timestamp.env
          - export TAG=${GITEE_PIPELINE_BUILD_NUMBER}-${TIMESTAMP_ENV}
          - echo "构建镜像 TAG: $TAG"
          - export TIMESTAMP_ENV_URL="http://your-server/path/to/file-${TIMESTAMP_ENV}.tar.gz"
          - echo "下载资源地址：$TIMESTAMP_ENV_URL"
          - wget "$TIMESTAMP_ENV_URL" -O ./output/product.tar.gz
        tag: ai4c-tcr.tencentcloudcr.com/ai4c-aibrain/agentfoundryui:${TAG}