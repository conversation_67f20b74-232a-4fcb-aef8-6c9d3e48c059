version: '1.0'
name: pipeline-20250313
displayName: pipeline-20250313
triggers:
  trigger: auto
  push:
    branches:
      precise:
        - master
stages:
  - name: stage-d61872ff
    displayName: 未命名
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: build@nodejs
        name: build_nodejs
        displayName: Nodejs 构建
        nodeVersion: 14.16.0
        commands:
          - '# 设置NPM源，提升安装速度'
          - npm config set registry https://registry.npmmirror.com
          - ''
          - '# 执行编译命令'
          - npm install && npm run build
        artifacts:
          - name: BUILD_ARTIFACT
            path:
              - ./dist
        caches:
          - ~/.npm
          - ~/.yarn
        notify: []
        strategy:
          retry: '0'
