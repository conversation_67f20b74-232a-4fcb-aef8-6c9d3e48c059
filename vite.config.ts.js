// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import svgr from "vite-plugin-svgr";
import { vitePluginForArco } from "@arco-plugins/vite-react";

// src/settings.json
var colorWeek = false;
var navbar = true;
var menu = true;
var footer = true;
var themeColor = "#4455f2";
var menuWidth = 246;
var settings_default = {
  colorWeek,
  navbar,
  menu,
  footer,
  themeColor,
  menuWidth
};

// vite.config.ts
import tailwindcss from "@tailwindcss/vite";
var vite_config_default = defineConfig(({ mode }) => {
  return {
    resolve: {
      alias: [{ find: "@", replacement: "/src" }]
    },
    plugins: [
      tailwindcss(),
      react(),
      svgr({
        include: [/\.svg$/]
      }),
      vitePluginForArco({
        theme: "@arco-themes/react-arco-pro",
        modifyVars: {
          "arcoblue-6": settings_default.themeColor,
          "gray-3": "#f5f5f5"
        }
      })
    ],
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true
        }
      }
    },
    server: {
      proxy: {
        "/api": {
          target: "https://bs-test-llm.ai4c.cn",
          changeOrigin: true,
          ws: true,
          rewrite: (path) => path.replace(/^\/api/, "")
        },
        "/v1": {
          target: "http://**********:9380",
          changeOrigin: true,
          ws: true
        }
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
