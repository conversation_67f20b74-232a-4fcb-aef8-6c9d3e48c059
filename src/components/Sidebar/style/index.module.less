
.dropdownMenu {
  position: absolute;
  bottom: 60px;
  left: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 8px;
  width: 250px;
  background: #FFFFFF;
  // box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08), 0px 2px 4px 0px rgba(0, 0, 0, 0.03);
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  z-index: 1000;
  border: 1px solid #f5f5f5;
  transform-origin: top center;
  transition: all 0.28s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.08));

  .avatarContainer {
    width: 100%;
    padding: 8px 12px;
    position: relative;
    box-sizing: border-box;
    margin-bottom: 16px;

    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 3%;
      width: 93%;
      height: 1px;
      background-color: #f5f5f5;
    }

    .avatarWrapper {
      background: #eee;
      color: white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      border: 1px solid #ebebeb;
      transition: transform 0.3s ease;

      .avatarImage {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
      }

      &:hover {
        transform: scale(1.05);
      }
    }

    .userName {
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      color: #333333;
    }

    .userEmail {
      color: #adadad;
      font-weight: 400;
      font-size: 12px;
      line-height: 16px;
    }
  }

  .menuItems {
    display: flex;
    flex-direction: column;
    width: 100%;

    .menuItem,
    .logoutMenuItem {
      opacity: 0;
      transform: translateY(8px);
      animation: fadeInUp 0.3s forwards;
      animation-delay: calc(0.05s * var(--item-index, 1));

      &:hover {
        transform: translateY(-1px);
        transition: all 0.2s ease;
      }

      &:active {
        transform: translateY(1px);
        transition: all 0.1s ease;
      }
    }

    .menuItem:nth-child(1) {
      --item-index: 1;
    }

    .menuItem:nth-child(2) {
      --item-index: 2;
    }

    .logoutMenuItem {
      --item-index: 3;
    }

    .menuItem {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 8px 12px;
      transition: all 0.2s;
      border-radius: 8px;

      &:hover {
        background: #fafafa;
      }

      .menuText {
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        color: #333333;
        margin-left: 8px;
      }
    }
  }

  /* 退出登录特殊样式 */
  .logoutMenuItem {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    transition: all 0.2s;
    border-radius: 8px;
    margin-top: 12px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: -8px;
      left: 3%;
      width: 93%;
      height: 1px;
      background-color: #f5f5f5;
    }

    &:hover {
      background-color: #fef8f8;
    }

    .logoutMenuText {
      font-weight: 600;
      font-size: 14px;
      line-height: 24px;
      color: #d54941;
      margin-left: 8px;
    }
  }
}

.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
  pointer-events: all;
  animation: popIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.hidden {
  opacity: 0;
  transform: translateY(10px) scale(0.95);
  pointer-events: none;
  display: flex;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(8px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }

  70% {
    transform: translateY(-2px) scale(1.01);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}