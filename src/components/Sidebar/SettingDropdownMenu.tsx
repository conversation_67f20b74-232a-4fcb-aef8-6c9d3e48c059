// components/SettingDropdownMenu.tsx
import React, { useCallback, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Message, Space, Typography } from '@arco-design/web-react';
import { UserInfo } from '@/types/userType';
import { triggerLogout } from '@/keycloak';
import { getAuthConfig } from '@/lib/services/AuthHelper';
import styles from './style/index.module.less';
import IconLogout from '@/assets/user/IconLogout.svg';
import IconAccount from '@/assets/user/IconAccount.svg';
import DefaultAvatar from '@/assets/user/DefaultAvatar.png';
// import IconSettings from '@/assets/user/IconSetting.svg';

const SettingDropdownMenu = ({
    userInfo,
    menuVisible,
    setMenuVisible,
}: {
    userInfo: UserInfo;
    menuVisible: boolean;
    setMenuVisible: (visible: boolean) => void;
}) => {
    const { Text } = Typography;
    const navigate = useNavigate();
    const dropdownRef = useRef<HTMLDivElement>(null);

    // 验证头像URL是否有效
    const isValidAvatarUrl = (url: string | null | undefined): boolean => {
        // 如果URL为空，则无效
        if (!url) return false;

        // 检查不完整的路径，如 "/user/avatar"
        if (url === '/user/avatar') return false;

        // 检查URL是否是有效的HTTP、HTTPS URL或data URL
        return url.startsWith('http://') || url.startsWith('https://') || url.startsWith('data:');
    };

    // 点击外部关闭菜单的处理函数
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
                !document.querySelector(`[key="user"]`)?.contains(event.target as Node)) {
                setMenuVisible(false);
            }
        };

        if (menuVisible) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [menuVisible, setMenuVisible]);

    // 设置选项
    // const handleSetting = () => {
    //     setMenuVisible(false);
    //     if (history.location.pathname !== '/user') {
    //         history.push('/user');
    //     }
    // };

    // 账户选项
    const handleAccount = () => {
        setMenuVisible(false);
        navigate('/user');
    };

    // 登出选项
    const handleLogout = useCallback(async () => {
        Message.success('已退出登录');
        const config = await getAuthConfig();
        // 使用Keycloak登出功能，并指定登出后跳转到登录页面
        triggerLogout(window.location.origin + '/login', config);
        setMenuVisible(false);
    }, []);

    if (!userInfo) {
        return null;
    }

    return (
        <div
            ref={dropdownRef}
            className={`${styles.dropdownMenu} ${menuVisible ? styles.visible : styles.hidden}`}
        >
            {/* 头像部分 */}
            <Space className={styles.avatarContainer} size={12}>
                <div className={styles.avatarWrapper}>
                    <img
                        src={isValidAvatarUrl(userInfo.avatar_url || userInfo.avatar) ? (userInfo.avatar_url || userInfo.avatar) : DefaultAvatar}
                        alt="头像"
                        className={styles.avatarImage}
                    />
                </div>
                <Space direction='vertical' size={0}>
                    <Text className={styles.userName}>{userInfo.full_name || ''}</Text>
                    <Text className={styles.userEmail}>{userInfo.email || ''}</Text>
                </Space>
            </Space>
            {/* 菜单项 */}
            <Space className={styles.menuItems} direction='vertical' size={4}>
                {/* 账户选项 */}
                <div
                    className={styles.menuItem}
                    onClick={handleAccount}
                >
                    <IconAccount />
                    <Text className={styles.menuText}>账户</Text>
                </div>

                {/* 设置选项 */}
                {/* <div
                    className={styles.menuItem}
                    onClick={handleSetting}
                >
                    <IconSettings />
                    <Text className={styles.menuText}>设置</Text>
                </div> */}

                {/* 登出选项 */}
                <div
                    className={styles.logoutMenuItem}
                    onClick={handleLogout}
                >
                    <IconLogout />
                    <Text className={styles.logoutMenuText}>退出登陆</Text>
                </div>
            </Space>
        </div>
    );
};

export default SettingDropdownMenu;