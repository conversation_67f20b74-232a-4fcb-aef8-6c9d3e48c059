import axios from 'axios';
import { useCallback, useEffect, useState } from 'react';

export const useCatchDocumentError = (url: string) => {
  const [error, setError] = useState<string>('');

  const fetchDocument = useCallback(async () => {
    const res = await axios.get(url);
    if (res.status !== 200) {
      setError("获取文件失败");
    }
  }, [url]);
  useEffect(() => {
    fetchDocument();
  }, [fetchDocument]);

  return error;
};
