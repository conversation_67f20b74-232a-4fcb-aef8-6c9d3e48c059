import React, {
  useState,
  useCallback,
  useMemo,
  useRef,
  useEffect,
} from 'react';
import { Checkbox } from '@arco-design/web-react';
import { FolderOutlined, FileOutlined } from '@ant-design/icons';
import { IconDown, IconUp } from '@arco-design/web-react/icon';

// 树节点数据类型
export interface TreeNode {
  key: string;
  title: string;
  icon?: React.ReactNode;
  isLeaf?: boolean;
  children?: TreeNode[];
  disabled?: boolean;
  checkable?: boolean;
  selectable?: boolean;
  [key: string]: any;
}

// 选中状态类型
export interface CheckedKeys {
  checked: string[];
  halfChecked: string[];
}

// 树组件属性类型
export interface TreePickerProps {
  /** 树形数据 */
  treeData: TreeNode[];
  /** 是否显示复选框 */
  checkable?: boolean;
  /** 是否严格模式（父子节点选中状态不关联） */
  checkStrictly?: boolean;
  /** 是否允许多选 */
  multiple?: boolean;
  /** 选中的节点 key 数组 */
  checkedKeys?: string[] | CheckedKeys;
  /** 选中的节点 key 数组 */
  selectedKeys?: string[];
  /** 展开的节点 key 数组 */
  expandedKeys?: string[];
  /** 是否显示图标 */
  showIcon?: boolean;
  /** 是否显示连接线 */
  showLine?: boolean;
  /** 节点前添加 Checkbox 复选框 */
  onCheck?: (checkedKeys: string[] | CheckedKeys, info: any) => void;
  /** 点击树节点触发 */
  onSelect?: (selectedKeys: string[], info: any) => void;
  /** 展开/收起节点时触发 */
  onExpand?: (expandedKeys: string[], info: any) => void;
  /** 异步加载数据 */
  loadData?: (node: TreeNode) => Promise<void>;
  /** 自定义渲染树节点 */
  titleRender?: (nodeData: TreeNode) => React.ReactNode;
  /** 自定义样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 节点高度 */
  nodeHeight?: number;
  /** 缩进距离 */
  indent?: number;
}

/**
 * 自定义树组件
 * 支持单选、多选、checkStrictly 模式以及自定义节点渲染
 */
const TreePicker: React.FC<TreePickerProps> = ({
  treeData = [],
  checkable = false,
  checkStrictly = false,
  multiple = false,
  checkedKeys = [],
  selectedKeys = [],
  expandedKeys = [],
  showIcon = true,
  showLine = false,
  onCheck,
  onSelect,
  onExpand,
  loadData,
  titleRender,
  className = '',
  style = {},
  nodeHeight = 32,
  indent = 24,
}) => {
  const [internalExpandedKeys, setInternalExpandedKeys] = useState<string[]>(
    []
  );
  const [internalSelectedKeys, setInternalSelectedKeys] = useState<string[]>(
    []
  );
  const [internalCheckedKeys, setInternalCheckedKeys] = useState<CheckedKeys>({
    checked: [],
    halfChecked: [],
  });
  const [loadingKeys, setLoadingKeys] = useState<string[]>([]);

  /**
   * 获取所有节点的映射
   */
  const nodeMap = useMemo(() => {
    const map = new Map<string, TreeNode>();
    const traverse = (nodes: TreeNode[], parent?: TreeNode) => {
      nodes.forEach((node) => {
        map.set(node.key, { ...node, parent });
        if (node.children) {
          traverse(node.children, node);
        }
      });
    };
    traverse(treeData);
    return map;
  }, [treeData]);

  /**
   * 检查节点是否有任何被选中的后代节点
   * @param nodeKey 节点key
   * @param checkedSet 选中节点的Set
   * @param nodeMap 节点映射
   * @returns 是否有被选中的后代节点
   */
  const hasAnyCheckedDescendants = useCallback(
    (
      nodeKey: string,
      checkedSet: Set<string>,
      nodeMap: Map<string, TreeNode>
    ): boolean => {
      const node = nodeMap.get(nodeKey);
      if (!node || !node.children) return false;

      for (const child of node.children) {
        if (
          checkedSet.has(child.key) ||
          hasAnyCheckedDescendants(child.key, checkedSet, nodeMap)
        ) {
          return true;
        }
      }
      return false;
    },
    []
  );

  // 获取当前展开的节点
  const currentExpandedKeys = expandedKeys || internalExpandedKeys;
  // 获取当前选中的节点
  const currentSelectedKeys = selectedKeys || internalSelectedKeys;
  // 获取当前勾选的节点
  const currentCheckedKeys = useMemo(() => {
    if (Array.isArray(checkedKeys)) {
      // 当外部传入的是数组时，需要计算半选状态
      const halfChecked: string[] = [];
      const checkedSet = new Set(checkedKeys);

      // 遍历所有有子节点的节点，计算半选状态
      nodeMap.forEach((node, key) => {
        if (node.children && node.children.length > 0) {
          const childKeys = node.children.map((child) => child.key);
          const checkedChildCount = childKeys.filter((childKey) =>
            checkedSet.has(childKey)
          ).length;
          const hasCheckedDescendants = hasAnyCheckedDescendants(
            key,
            checkedSet,
            nodeMap
          );

          // 如果有部分子节点或后代节点被选中，但当前节点未被选中，则为半选状态
          if (
            (checkedChildCount > 0 && checkedChildCount < childKeys.length) ||
            (hasCheckedDescendants && !checkedSet.has(key))
          ) {
            halfChecked.push(key);
          }
        }
      });

      return { checked: checkedKeys, halfChecked };
    }
    return checkedKeys || internalCheckedKeys;
  }, [checkedKeys, internalCheckedKeys, nodeMap, hasAnyCheckedDescendants]);

  /**
   * 监听 treeData 变化，强制重新渲染
   */
  useEffect(() => {
    // 当 treeData 发生变化时，强制组件重新渲染
    // 这确保了懒加载的数据能够正确显示
  }, [treeData]);

  /**
   * 获取节点的所有子节点 key
   */
  const getChildrenKeys = useCallback(
    (nodeKey: string): string[] => {
      const node = nodeMap.get(nodeKey);
      if (!node || !node.children) return [];

      const keys: string[] = [];
      const traverse = (children: TreeNode[]) => {
        children.forEach((child) => {
          keys.push(child.key);
          if (child.children) {
            traverse(child.children);
          }
        });
      };
      traverse(node.children);
      return keys;
    },
    [nodeMap]
  );

  /**
   * 获取节点的所有父节点 key
   */
  const getParentKeys = useCallback(
    (nodeKey: string): string[] => {
      const keys: string[] = [];
      let current = nodeMap.get(nodeKey)?.parent;
      while (current) {
        keys.push(current.key);
        current = nodeMap.get(current.key)?.parent;
      }
      return keys;
    },
    [nodeMap]
  );

  /**
   * 计算半选状态
   * 递归计算所有父节点的半选状态，考虑子节点和孙子节点的选中情况
   */
  const calculateHalfChecked = useCallback(
    (checkedKeys: string[]): string[] => {
      const halfChecked: string[] = [];
      const checkedSet = new Set(checkedKeys);

      /**
       * 递归检查节点的选中状态
       * @param nodeKey 节点key
       * @returns {checked: boolean, hasChecked: boolean, hasUnchecked: boolean}
       */
      const checkNodeStatus = (
        nodeKey: string
      ): { checked: boolean; hasChecked: boolean; hasUnchecked: boolean } => {
        const node = nodeMap.get(nodeKey);
        if (!node)
          return { checked: false, hasChecked: false, hasUnchecked: true };

        // 如果是叶子节点，直接返回其选中状态
        if (!node.children || node.children.length === 0) {
          const isChecked = checkedSet.has(nodeKey);
          return {
            checked: isChecked,
            hasChecked: isChecked,
            hasUnchecked: !isChecked,
          };
        }

        // 对于有子节点的节点，递归检查所有子节点
        let hasChecked = false;
        let hasUnchecked = false;

        for (const child of node.children) {
          const childStatus = checkNodeStatus(child.key);
          if (childStatus.hasChecked) hasChecked = true;
          if (childStatus.hasUnchecked) hasUnchecked = true;
        }

        const isCurrentChecked = checkedSet.has(nodeKey);

        return {
          checked: isCurrentChecked,
          hasChecked: hasChecked || isCurrentChecked,
          hasUnchecked: hasUnchecked || !isCurrentChecked,
        };
      };

      // 遍历所有有子节点的节点，计算半选状态
      nodeMap.forEach((node, key) => {
        if (node.children && node.children.length > 0) {
          const status = checkNodeStatus(key);
          // 如果既有选中的子节点又有未选中的子节点，则为半选状态
          if (status.hasChecked && status.hasUnchecked && !status.checked) {
            halfChecked.push(key);
          }
        }
      });

      return halfChecked;
    },
    [nodeMap]
  );

  /**
   * 处理节点展开/收起
   */
  const handleExpand = useCallback(
    async (nodeKey: string, expanded: boolean) => {
      const node = nodeMap.get(nodeKey);
      if (!node) return;

      let newExpandedKeys: string[];
      if (expanded) {
        newExpandedKeys = [...currentExpandedKeys, nodeKey];

        // 如果有异步加载数据的需求
        if (loadData && !node.children && !node.isLeaf) {
          setLoadingKeys((prev) => [...prev, nodeKey]);
          try {
            await loadData(node);
          } catch (error) {
            console.error('加载数据失败:', error);
          } finally {
            setLoadingKeys((prev) => prev.filter((key) => key !== nodeKey));
          }
        }
      } else {
        newExpandedKeys = currentExpandedKeys.filter((key) => key !== nodeKey);
      }

      if (!expandedKeys) {
        setInternalExpandedKeys(newExpandedKeys);
      }

      onExpand?.(newExpandedKeys, { node, expanded });
    },
    [currentExpandedKeys, expandedKeys, loadData, nodeMap, onExpand]
  );

  /**
   * 处理节点选择
   */
  const handleSelect = useCallback(
    (nodeKey: string) => {
      const node = nodeMap.get(nodeKey);
      if (!node || node.disabled || node.selectable === false) return;

      let newSelectedKeys: string[];
      if (multiple) {
        if (currentSelectedKeys.includes(nodeKey)) {
          newSelectedKeys = currentSelectedKeys.filter(
            (key) => key !== nodeKey
          );
        } else {
          newSelectedKeys = [...currentSelectedKeys, nodeKey];
        }
      } else {
        newSelectedKeys = currentSelectedKeys.includes(nodeKey)
          ? []
          : [nodeKey];
      }

      if (!selectedKeys) {
        setInternalSelectedKeys(newSelectedKeys);
      }

      onSelect?.(newSelectedKeys, {
        node,
        selected: newSelectedKeys.includes(nodeKey),
      });
    },
    [currentSelectedKeys, selectedKeys, multiple, nodeMap, onSelect]
  );

  /**
   * 处理节点勾选
   */
  const handleCheck = useCallback(
    (nodeKey: string, checked: boolean) => {
      const node = nodeMap.get(nodeKey);
      if (!node || node.disabled || node.checkable === false) return;

      let newCheckedKeys: string[];

      if (checkStrictly) {
        // 严格模式：父子节点选中状态不关联
        if (checked) {
          newCheckedKeys = [...currentCheckedKeys.checked, nodeKey];
        } else {
          newCheckedKeys = currentCheckedKeys.checked.filter(
            (key) => key !== nodeKey
          );
        }
      } else {
        // 非严格模式：父子节点选中状态关联
        const childrenKeys = getChildrenKeys(nodeKey);
        const parentKeys = getParentKeys(nodeKey);

        if (checked) {
          // 选中当前节点及所有子节点
          newCheckedKeys = [
            ...new Set([
              ...currentCheckedKeys.checked,
              nodeKey,
              ...childrenKeys,
            ]),
          ];

          // 递归检查并更新父节点的选中状态
          parentKeys.forEach((parentKey) => {
            const parentNode = nodeMap.get(parentKey);
            if (parentNode?.children) {
              const siblingKeys = parentNode.children.map((child) => child.key);
              const allSiblingsChecked = siblingKeys.every((key) =>
                newCheckedKeys.includes(key)
              );
              if (allSiblingsChecked && !newCheckedKeys.includes(parentKey)) {
                newCheckedKeys.push(parentKey);
              }
            }
          });
        } else {
          // 取消选中当前节点及所有子节点
          newCheckedKeys = currentCheckedKeys.checked.filter(
            (key) => key !== nodeKey && !childrenKeys.includes(key)
          );

          // 递归检查并更新父节点的选中状态
          parentKeys.forEach((parentKey) => {
            const parentNode = nodeMap.get(parentKey);
            if (parentNode?.children) {
              const siblingKeys = parentNode.children.map((child) => child.key);
              const hasAnyCheckedSibling = siblingKeys.some((key) =>
                newCheckedKeys.includes(key)
              );
              // 如果没有任何兄弟节点被选中，则取消父节点的选中状态
              if (!hasAnyCheckedSibling && newCheckedKeys.includes(parentKey)) {
                newCheckedKeys = newCheckedKeys.filter(
                  (key) => key !== parentKey
                );
              }
            }
          });
        }
      }

      const newHalfChecked = checkStrictly
        ? []
        : calculateHalfChecked(newCheckedKeys);
      const result = { checked: newCheckedKeys, halfChecked: newHalfChecked };

      if (!checkedKeys) {
        setInternalCheckedKeys(result);
      }

      onCheck?.(checkStrictly ? result : newCheckedKeys, { node, checked });
    },
    [
      currentCheckedKeys,
      checkedKeys,
      checkStrictly,
      nodeMap,
      getChildrenKeys,
      getParentKeys,
      calculateHalfChecked,
      onCheck,
    ]
  );

  /**
   * 渲染树节点
   */
  const renderTreeNode = useCallback(
    (node: TreeNode, level: number = 0): React.ReactNode => {
      const isExpanded = currentExpandedKeys.includes(node.key);
      const isSelected = currentSelectedKeys.includes(node.key);
      const isChecked = currentCheckedKeys.checked.includes(node.key);
      const isHalfChecked = currentCheckedKeys.halfChecked.includes(node.key);
      const isLoading = loadingKeys.includes(node.key);
      const hasChildren = node.children && node.children.length > 0;
      const canExpand = hasChildren || (!node.isLeaf && loadData);

      return (
        <div key={node.key}>
          {/* 节点内容 */}
          <div
            className={`tree-node ${isSelected ? 'tree-node-selected' : ''} ${
              node.disabled ? 'tree-node-disabled' : ''
            }`}
            style={{
              height: nodeHeight,
              paddingLeft: level * indent,
              display: 'flex',
              alignItems: 'center',
              cursor: node.disabled ? 'not-allowed' : 'pointer',
              backgroundColor: isSelected ? '#e6f7ff' : 'transparent',
              borderRadius: 4,
            }}
            onClick={(e) => {
              e.stopPropagation();
              if (!node.disabled) {
                // 如果点击的是节点标题区域且节点可展开，则展开/收起节点
                if (canExpand && e.target === e.currentTarget) {
                  handleExpand(node.key, !isExpanded);
                } else {
                  handleSelect(node.key);
                }
              }
            }}
          >
            {/* 复选框 */}
            {checkable && node.checkable !== false && (
              <Checkbox
                checked={isChecked}
                indeterminate={isHalfChecked}
                disabled={node.disabled}
                onChange={(checked: boolean, e: Event) => {
                  e.stopPropagation();
                  handleCheck(node.key, checked);
                }}
                style={{ marginRight: 8 }}
              />
            )}

            {/* 节点图标 */}
            {showIcon && (
              <span style={{ marginRight: 8, fontSize: 14 }}>
                {node.icon ||
                  (node.isLeaf ? <FileOutlined /> : <FolderOutlined />)}
              </span>
            )}

            {/* 节点标题 */}
            <span
              className="tree-node-title"
              style={{
                flex: 1,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                cursor: canExpand ? 'pointer' : 'default',
              }}
              onClick={(e) => {
                e.stopPropagation();
                if (!node.disabled && canExpand) {
                  handleExpand(node.key, !isExpanded);
                }
              }}
            >
              {titleRender ? titleRender(node) : node.title}
            </span>
            {/* 展开/收起图标 */}
            <div
              className="tree-switcher"
              style={{
                width: 16,
                height: 16,
                marginRight: 4,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: canExpand ? 'pointer' : 'default',
              }}
              onClick={(e) => {
                e.stopPropagation();
                if (canExpand) {
                  handleExpand(node.key, !isExpanded);
                }
              }}
            >
              {isLoading ? (
                <div
                  className="loading-spinner"
                  style={{
                    width: 12,
                    height: 12,
                    border: '1px solid #ccc',
                    borderTop: '1px solid #1890ff',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite',
                  }}
                />
              ) : canExpand ? (
                isExpanded ? (
                  <IconDown />
                ) : (
                  <IconUp />
                )
              ) : null}
            </div>
          </div>

          {/* 子节点 */}
          {isExpanded && hasChildren && (
            <div className="tree-children">
              {node.children!.map((child) => renderTreeNode(child, level + 1))}
            </div>
          )}
        </div>
      );
    },
    [
      currentExpandedKeys,
      currentSelectedKeys,
      currentCheckedKeys,
      loadingKeys,
      nodeHeight,
      indent,
      checkable,
      showIcon,
      titleRender,
      handleExpand,
      handleSelect,
      handleCheck,
    ]
  );

  return (
    <div
      className={`tree-picker ${className}`}
      style={{
        ...style,
        fontSize: 14,
        lineHeight: 1.5,
      }}
    >
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          .tree-node {
            transition: background-color 0.2s ease;
          }
          .tree-node:hover {
            background-color: rgba(0, 0, 0, 0.04) !important;
          }
          .tree-node-selected {
            background-color: #e6f7ff;
          }
          .tree-node-selected:hover {
            background-color: #bae7ff;
          }
          .tree-node-disabled {
            color: #ccc;
            cursor: not-allowed;
          }
          .tree-node-disabled:hover {
            background-color: transparent;
          }

          .tree-switcher:hover {
            background-color: rgba(0, 0, 0, 0.06);
            border-radius: 2px;
            transition: background-color 0.2s ease;
          }
        `}
      </style>
      {treeData.map((node) => renderTreeNode(node))}
    </div>
  );
};

export default TreePicker;
