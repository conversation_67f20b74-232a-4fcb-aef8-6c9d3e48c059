import React, { useState, useMemo, useEffect, useRef } from 'react';
import {
  Modal,
  Tree,
  Checkbox,
  Image,
  Input,
  Button,
  Spin,
  Select,
  Tooltip,
  Message,
} from '@arco-design/web-react';
import IconClose from '@/assets/application/close.svg';
import styles from './style/index.module.less';
import IconSearch from '@/assets/application/search.svg';
import group from '@/assets/application/folderIcon.png';
import agent from '@/assets/application/agentIcon1.png';
import TimeCard from '@/assets/application/time_card.png';
import workflow from '@/assets/application/workflowIcon.png';
import IconKnowledge from '@/assets/knowledge/IconKnowledge.png';
import AcpServerIcon from '@/assets/acp/acpServer.png';
import fileIcon from '@/assets/application/fileIcon.png';
import useLocale from '@/utils/useLocale';
import Text from '@arco-design/web-react/es/Typography/text';
import ColComponent from '@arco-design/web-react/es/Grid/col';
import RowComponent from '@arco-design/web-react/es/Grid/row';
import ButtonComponent from '@arco-design/web-react/es/Button';
import { getSystemToolLabelOptions } from '@/lib/services/utilities-service';
import { getKnowledgeLabels } from '@/lib/services/knowledge-service';
import IconRight from '@/assets/application/IconRight.svg';
import IconDown from '@/assets/application/IconDown.svg';
import { getTimeSequenceCardMetadataList } from '@/lib/services/timeSequenceCard-service';
import { getAcpToolsById } from '@/lib/services/acp-server-service';
import { checkAcpServerAvailable } from '@/lib/services/acp-server-service';
import { fetchKnowledgeCollections } from '@/pages/knowledge/components/knowledge/services/aiStaff-service';

const Option = Select.Option;

const TreeModal = ({
  type,
  title,
  visible,
  onClose,
  treeData,
  checkedIds,
  onCheck,
  onConfirm,
  onSearch,
  loading,
  agentTimeSequenceCards,
  acpTimeSequenceCardSelections,
}) => {
  const locale = useLocale();
  const [searchValue, setSearchValue] = useState('');
  const searchTimerRef = useRef(null);
  const [labelSearch, setLabelSearch] = useState<string>('');
  const [labelOptions, setLabelOptions] = useState<
    { value: string; label: string }[]
  >([]);
  const [expandedNodes, setExpandedNodes] = useState<{
    [key: string]: boolean;
  }>({});
  const [timeSequenceCards, setTimeSequenceCards] = useState<
    { value: string; label: string }[]
  >([]);
  const [selectedTimeSequenceCards, setSelectedTimeSequenceCards] = useState<{
    [key: string]: string;
  }>({});
  const [loadingTimeSequenceCards, setLoadingTimeSequenceCards] =
    useState(false);
  const [loadingNodes, setLoadingNodes] = useState<{ [key: string]: boolean }>(
    {}
  );

  const showLabelFiltrate = useMemo(
    () => ['tools', 'knowledge'].includes(type),
    [type]
  );

  useEffect(() => {
    setLabelOptions([]);
    setLabelSearch('');

    if (type === 'tools') {
      getSystemToolLabelOptions().then((res) => {
        setLabelOptions(res.data.map((item) => ({ value: item, label: item })));
      });
    }

    if (type === 'acpServer') {
      // 使用当前agent已选择的时序卡片数据
      if (agentTimeSequenceCards && Array.isArray(agentTimeSequenceCards)) {
        setTimeSequenceCards(
          agentTimeSequenceCards.map((item) => ({
            value: item.id,
            label: item.name || item.title || item.display_name,
          }))
        );
      } else {
        setTimeSequenceCards([]);
      }
    }

    if (!visible) {
      setSearchValue('');
      if (searchTimerRef.current) {
        clearTimeout(searchTimerRef.current);
      }
    }
  }, [visible, type]);

  // 初始化时重置折叠状态
  useEffect(() => {
    if (visible && treeData.length > 0) {
      // 默认折叠所有节点
      const initialExpandState = {};
      treeData.forEach((node) => {
        initialExpandState[node.id] = false;
      });
      setExpandedNodes(initialExpandState);

      if (type === 'acpServer') {
        const preSelectedCards = {};
        treeData.forEach((server) => {
          if (server.children) {
            server.children.forEach((tool) => {
              if (tool.preSelectedTimeSequenceCard) {
                preSelectedCards[tool.id] = tool.preSelectedTimeSequenceCard;
              }
            });
          }
        });
        setSelectedTimeSequenceCards(preSelectedCards);
      }
    }
  }, [visible, treeData, type]);

  // 处理节点折叠/展开
  const toggleNodeExpand = async (nodeId) => {
    const node = findNodeById(treeData, nodeId);

    if (
      type === 'acpServer' &&
      node &&
      node.needsLazyLoad &&
      !expandedNodes[nodeId]
    ) {
      // 设置加载状态
      setLoadingNodes((prev) => ({ ...prev, [nodeId]: true }));

      try {
        const tools = await getAcpToolsById(nodeId);
        if (tools && Array.isArray(tools)) {
          // 获取预选择的时序卡片信息
          const timeSequenceCardSelections =
            acpTimeSequenceCardSelections || {};

          // 更新节点的子节点
          node.children = tools.map((tool) => {
            const toolId = `${nodeId}-${tool.name}`;
            return {
              id: toolId,
              title: tool.name,
              description: tool.description,
              parentId: nodeId,
              level: 2,
              type: 'tool',
              name: tool.name,
              jsonSchema: tool.jsonSchema,
              preSelectedTimeSequenceCard: timeSequenceCardSelections[toolId],
            };
          });
          node.needsLazyLoad = false;

          // 更新selectedTimeSequenceCards状态
          const newSelectedCards = { ...selectedTimeSequenceCards };
          node.children.forEach((tool) => {
            if (tool.preSelectedTimeSequenceCard) {
              newSelectedCards[tool.id] = tool.preSelectedTimeSequenceCard;
            }
          });
          setSelectedTimeSequenceCards(newSelectedCards);
        }
      } catch (error) {
        console.error(`获取服务器 ${nodeId} 的工具列表失败:`, error);
      } finally {
        // 清除加载状态
        setLoadingNodes((prev) => {
          const newState = { ...prev };
          delete newState[nodeId];
          return newState;
        });
      }
    }

    setExpandedNodes((prev) => ({
      ...prev,
      [nodeId]: !prev[nodeId],
    }));
  };

  // 处理搜索输入
  const handleSearchChange = (value) => {
    setSearchValue(value);

    if (searchTimerRef.current) {
      clearTimeout(searchTimerRef.current);
    }

    if (!visible) return;

    // 无论是否为空值都触发搜索
    searchTimerRef.current = setTimeout(() => {
      if (onSearch) {
        onSearch({
          name: value,
          label: labelSearch || '',
        });
      }
    }, 300);
  };

  // 处理标签变化
  const handleLabelChange = (value: string) => {
    setLabelSearch(value);

    // 当标签改变时也触发搜索
    if (onSearch) {
      onSearch({
        name: searchValue,
        label: value || '',
      });
    }
  };

  // 查找节点工具函数
  const findNodeById = (nodes, id) => {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // 计算选中叶子节点数量
  const selectedCount = useMemo(() => {
    return checkedIds.filter((id) => {
      const node = findNodeById(treeData, id);
      return node && (!node.children || node.children.length === 0);
    }).length;
  }, [checkedIds, treeData]);

  // 递归获取所有子节点ID（包括嵌套子节点）
  const getAllChildIds = (node) => {
    let ids = [];
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => {
        ids.push(child.id);
        ids = ids.concat(getAllChildIds(child));
      });
    } else if (node.childrenData && node.childrenData.length > 0) {
      node.childrenData.forEach((child) => {
        ids.push(child.id);
        ids = ids.concat(getAllChildIds(child));
      });
    }
    return ids;
  };

  // 递归查找父节点并更新状态
  const updateParentStatus = (nodeId, newCheckedIds) => {
    const node = findNodeById(treeData, nodeId);
    if (!node?.parentId) return;

    const parent = findNodeById(treeData, node.parentId);
    if (!parent?.children) return;

    // 计算父节点的子节点选中情况
    const allChecked = parent.children.every(
      (child) =>
        newCheckedIds.includes(child.id) &&
        getAllChildIds(child).every((cid) => newCheckedIds.includes(cid))
    );

    // 更新父节点状态
    let updatedIds = [...newCheckedIds];
    if (allChecked && !updatedIds.includes(parent.id)) {
      updatedIds.push(parent.id);
    } else if (!allChecked && updatedIds.includes(parent.id)) {
      updatedIds = updatedIds.filter((id) => id !== parent.id);
    }

    // 递归更新祖先节点
    if (JSON.stringify(updatedIds) !== JSON.stringify(newCheckedIds)) {
      updateParentStatus(parent.id, updatedIds);
      return updatedIds;
    }
    return newCheckedIds;
  };

  // 处理选中/取消选中
  const handleCheck = async (node, checked) => {
    let newCheckedIds = [...checkedIds];

    // 如果是ACP server父节点且需要懒加载，先加载子节点
    if (
      type === 'acpServer' &&
      node.level === 1 &&
      node.needsLazyLoad &&
      checked
    ) {
      // 设置加载状态
      setLoadingNodes((prev) => ({ ...prev, [node.id]: true }));

      try {
        const result = await checkAcpServerAvailable(node.id);
        if (result.success) {
          Message.success(result.message || '测试连接成功');
          const tools = await getAcpToolsById(node.id);
          if (tools && Array.isArray(tools)) {
            // 获取预选择的时序卡片信息
            const timeSequenceCardSelections =
              acpTimeSequenceCardSelections || {};

            // 更新节点的子节点
            node.children = tools.map((tool) => {
              const toolId = `${node.id}-${tool.name}`;
              return {
                id: toolId,
                title: tool.name,
                description: tool.description,
                parentId: node.id,
                level: 2,
                type: 'tool',
                name: tool.name,
                jsonSchema: tool.jsonSchema,
                preSelectedTimeSequenceCard: timeSequenceCardSelections[toolId],
              };
            });
            node.needsLazyLoad = false;

            // 更新selectedTimeSequenceCards状态
            const newSelectedCards = { ...selectedTimeSequenceCards };
            node.children.forEach((tool) => {
              if (tool.preSelectedTimeSequenceCard) {
                newSelectedCards[tool.id] = tool.preSelectedTimeSequenceCard;
              }
            });
            setSelectedTimeSequenceCards(newSelectedCards);
          }
        } else {
          Message.error(result.message || '测试连接失败，请检查配置');
          return;
        }
      } catch (error) {
        console.error(`获取服务器 ${node.id} 的工具列表失败:`, error);
        return;
      } finally {
        // 清除加载状态
        setLoadingNodes((prev) => {
          const newState = { ...prev };
          delete newState[node.id];
          return newState;
        });
      }
    }

    const childIds = getAllChildIds(node);

    if (checked) {
      // 如果是ACP服务器的工具节点，检查是否需要特殊处理
      if (type === 'acpServer' && node.level === 2 && node.type === 'tool') {
        // 添加工具节点ID
        newCheckedIds = Array.from(new Set([...newCheckedIds, node.id]));

        // 如果选择了工具，同时选中其父服务器
        if (!newCheckedIds.includes(node.parentId)) {
          newCheckedIds.push(node.parentId);
        }

        // 如果有选择时序卡片，将节点ID和时序卡片ID的关系存储到node的timeSequenceCardId属性
        if (selectedTimeSequenceCards[node.id]) {
          node.timeSequenceCardId = selectedTimeSequenceCards[node.id];
          console.log(
            `设置节点 ${node.id} 的时序卡片ID为 ${node.timeSequenceCardId}`
          );
        }
      } else {
        // 常规选中处理，同时选中所有子节点
        newCheckedIds = Array.from(
          new Set([...newCheckedIds, node.id, ...childIds])
        );
      }
    } else {
      if (type === 'acpServer' && node.level === 2 && node.type === 'tool') {
        // 移除工具节点ID
        newCheckedIds = newCheckedIds.filter((id) => id !== node.id);

        // 检查同级工具是否还有被选中的
        const parent = findNodeById(treeData, node.parentId);
        const siblingToolsStillSelected =
          parent &&
          parent.children &&
          parent.children.some(
            (tool) => tool.id !== node.id && newCheckedIds.includes(tool.id)
          );

        // 如果没有同级工具被选中，也取消选中父服务器
        if (!siblingToolsStillSelected) {
          newCheckedIds = newCheckedIds.filter((id) => id !== node.parentId);
        }

        // 取消选中时，移除时序卡片关联
        if (node.timeSequenceCardId) {
          delete node.timeSequenceCardId;
          console.log(`移除节点 ${node.id} 的时序卡片ID关联`);
        }
      } else if (type === 'acpServer' && node.level === 1) {
        // 如果取消选中服务器，同时取消所有子工具
        newCheckedIds = newCheckedIds.filter(
          (id) => id !== node.id && !childIds.includes(id)
        );
      } else {
        // 常规取消选中处理
        newCheckedIds = newCheckedIds.filter(
          (id) => id !== node.id && !childIds.includes(id)
        );
      }
    }

    // 自底向上更新父节点状态，但对ACP工具节点特殊处理
    if (!(type === 'acpServer' && node.level === 2 && node.type === 'tool')) {
      newCheckedIds =
        updateParentStatus(node.id, newCheckedIds) || newCheckedIds;
    }

    // 自底向上更新祖节点
    if (node.level === 3) {
      const parent = findNodeById(treeData, node.parentId);
      if (parent) {
        newCheckedIds =
          updateParentStatus(parent.id, newCheckedIds) || newCheckedIds;
      }
    }

    onCheck(Array.from(new Set(newCheckedIds)));
    onConfirm(Array.from(new Set(newCheckedIds)));
  };

  const isIndeterminate = (node) => {
    if (!node.childrenData) return false;

    const ids = getAllChildIds(node);
    if (ids.every((element) => checkedIds.includes(element))) {
      return false;
    }

    return node.childrenData.some(
      (child) =>
        checkedIds.includes(child.id) ||
        (child.childrenData && isIndeterminate(child))
    );
  };

  // 自定义树结构渲染
  const renderCustomTree = (data) => {
    if (!data || data.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '16px 0' }}>
          {searchValue ? '没有找到匹配的结果' : '暂无数据'}
        </div>
      );
    }

    return (
      <div className={styles.customTreeContainer}>
        {data.map((node) => renderNode(node))}
      </div>
    );
  };

  // 渲染单个节点
  const renderNode = (node) => {
    const hasChildren =
      (node.children && node.children.length > 0) || node.needsLazyLoad;
    const isExpanded = expandedNodes[node.id];
    const isLoading = loadingNodes[node.id];

    return (
      <div key={node.id} className={styles.nodeContainer}>
        <div
          className={styles.nodeRow}
          onClick={hasChildren ? () => toggleNodeExpand(node.id) : undefined}
        >
          {renderTitle(node)}

          {hasChildren && (
            <div className={styles.expandIcon}>
              {isLoading ? (
                <Spin size={12} />
              ) : isExpanded ? (
                <IconDown />
              ) : (
                <IconRight />
              )}
            </div>
          )}
        </div>

        {hasChildren &&
          isExpanded &&
          node.children &&
          node.children.length > 0 && (
            <div className={styles.childrenContainer}>
              {node.children.map((child) => renderNode(child))}
            </div>
          )}
      </div>
    );
  };

  // 自定义节点渲染
  const renderTitle = (node) => {
    const isLeaf =
      !(node.children && node.children.length > 0) && !node.needsLazyLoad;
    const checked = checkedIds.includes(node.id);
    const indeterminate = isIndeterminate(node);

    const processedLabels = Array.isArray(node.labels)
      ? node.labels
          .map((item) => {
            try {
              return typeof item === 'string'
                ? item.replace(/[\[\]"\\]/g, '').trim()
                : item;
            } catch {
              return item;
            }
          })
          .filter((item) => typeof item === 'string' && item.length > 0)
      : [];

    const isAcpTool =
      type === 'acpServer' && node.level === 2 && node.type === 'tool';

    if (isAcpTool) {
      // ACP服务器子节点的特殊布局
      return (
        <div className={styles.customTreeRow}>
          <Image className={styles.customIcon} src={fileIcon} />
          {/* 左侧内容区 */}
          <div className={styles.acpServerLeftContentArea}>
            <div className={styles.nameArea}>
              <div className={styles.name}>{node.title}</div>
              {node.description && (
                <Tooltip
                  content={node.description}
                  position="top"
                  style={{ maxWidth: 300 }}
                >
                  <div
                    className={styles.description}
                    style={{
                      whiteSpace: 'normal',
                      display: '-webkit-box',
                      WebkitBoxOrient: 'vertical',
                      WebkitLineClamp: 3,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      maxHeight: '60px',
                      lineHeight: '20px',
                    }}
                  >
                    {node.description}
                  </div>
                </Tooltip>
              )}
              {processedLabels.length > 0 && (
                <div className={styles.labels}>
                  {processedLabels.map((label, index) => (
                    <span key={index} className={styles.label}>
                      {label}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* 右侧操作区 */}
          <div
            className={styles.acpServerRightSideArea}
            onClick={(e) => e.stopPropagation()}
          >
            {/* 上部分：时序卡片选择器 */}
            <div className={styles.timeSequenceCardSelector}>
              <Select
                placeholder={
                  locale['menu.application.info.setting.addTimeSequenceCard']
                }
                style={{ width: '100%' }}
                value={selectedTimeSequenceCards[node.id] || undefined}
                onChange={(value) =>
                  handleTimeSequenceCardChange(node.id, value)
                }
                allowClear
              >
                {timeSequenceCards.map((card) => (
                  <Option key={card.value} value={card.value}>
                    {card.label}
                  </Option>
                ))}
              </Select>
            </div>

            {/* 下部分：添加/移除按钮 */}
            <div className={styles.buttonArea}>
              {checked && (
                <div className={styles.addedTextArea}>
                  <Text className={styles.addedText}>已添加</Text>
                </div>
              )}

              <div className={styles.actionArea}>
                <Button
                  className={`${styles.actionButton} ${
                    checked ? styles.remove : styles.add
                  }`}
                  onClick={() => handleCheck(node, !checked)}
                >
                  {checked ? '移除' : '添加'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // 其他类型节点保持原样
    return (
      <div className={styles.customTreeRow}>
        <Image
          className={styles.customIcon}
          src={
            type === 'agent'
              ? node.icon_url
                ? node.icon_url
                : agent
              : type === 'workflow'
              ? node.icon_url
                ? node.icon_url
                : workflow
              : type === 'knowledge'
              ? node.level === 1
                ? IconKnowledge
                : node.level === 2
                ? group
                : fileIcon
              : type === 'acpServer'
              ? AcpServerIcon
              : TimeCard
          }
        />
        <div className={styles.contentArea}>
          <div className={styles.nameArea}>
            <div className={styles.name}>{node.title}</div>
            {node.description && (
              <Tooltip
                content={node.description}
                position="top"
                style={{ maxWidth: 300 }}
              >
                <div className={styles.description}>{node.description}</div>
              </Tooltip>
            )}
            {processedLabels.length > 0 && (
              <div className={styles.labels}>
                {processedLabels.map((label, index) => (
                  <span key={index} className={styles.label}>
                    {label}
                  </span>
                ))}
              </div>
            )}
          </div>

          {type !== 'knowledge' && (
            <div className={styles.meta}>
              <span className={styles.metaItem}>
                创建时间:{' '}
                {node.createdTime
                  ? new Date(node.createdTime).toLocaleString('zh-CN', {
                      timeZone: 'UTC',
                    })
                  : '-'}
              </span>
            </div>
          )}
        </div>
        <div className={styles.rightArea} onClick={(e) => e.stopPropagation()}>
          {checked && (
            <div className={styles.addedTextArea}>
              <Text className={styles.addedText}>已添加</Text>
            </div>
          )}

          <div className={styles.actionArea}>
            <Button
              className={`${styles.actionButton} ${
                checked ? styles.remove : styles.add
              }`}
              onClick={() => handleCheck(node, !checked)}
            >
              {checked ? '移除' : '添加'}
            </Button>
          </div>
        </div>
      </div>
    );
  };

  // 处理时序卡片选择
  const handleTimeSequenceCardChange = (nodeId, value) => {
    setSelectedTimeSequenceCards((prev) => ({
      ...prev,
      [nodeId]: value,
    }));

    // 找到对应的节点
    const node = findNodeById(treeData, nodeId);
    if (node) {
      // 更新节点属性
      node.timeSequenceCardId = value;

      // 如果节点已被选中，更新选中状态
      if (checkedIds.includes(nodeId)) {
        // 重新调用onCheck和onConfirm以更新选中状态
        onCheck([...checkedIds]);
        onConfirm([...checkedIds]);
      }
    }
  };

  // 渲染标题区域
  const renderModalTitle = () => {
    return title;
  };

  return (
    <Modal
      title={renderModalTitle()}
      visible={visible}
      onOk={() => {
        onClose();
      }}
      onCancel={onClose}
      className={styles.customModal}
      footer={null}
      closeIcon={<IconClose />}
    >
      <RowComponent className={styles.searchRow}>
        <Input
          className={styles.searchBox}
          prefix={<IconSearch />}
          placeholder={
            locale['menu.application.header.basic.search.placeholder']
          }
          value={searchValue}
          onChange={handleSearchChange}
          onClear={() => {
            setSearchValue('');
            onSearch && onSearch('');
          }}
          allowClear
        />
        {type !== 'acpServer' && (
          <Select
            placeholder={locale['menu.application.agent.search.tags']}
            className={styles.selectBox}
            value={labelSearch || undefined}
            onChange={handleLabelChange}
            allowClear
            triggerProps={{
              autoAlignPopupWidth: false,
              autoAlignPopupMinWidth: true,
              position: 'bl',
            }}
          >
            {labelOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        )}
      </RowComponent>

      <div
        style={{ borderTop: '1px solid rgba(0, 0, 0, 0.1)', margin: '16px 0' }}
      />

      <div className={styles.treeContainer}>
        <Spin loading={loading} style={{ display: 'block', height: '100%' }}>
          {!treeData || treeData.length === 0 ? (
            <div
              style={{
                textAlign: 'center',
                padding: '40px 16px',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'rgba(0, 0, 0, 0.45)',
              }}
            >
              {searchValue ? '没有找到匹配的结果' : '暂无数据'}
            </div>
          ) : (
            <>
              {/* 替换树组件为自定义渲染 */}
              {renderCustomTree(
                type === 'tools'
                  ? treeData.map((item) => ({
                      ...item,
                      children: [], // 清空子节点，只展示父节点
                    }))
                  : treeData
              )}
            </>
          )}
        </Spin>
      </div>
    </Modal>
  );
};

export default TreeModal;
