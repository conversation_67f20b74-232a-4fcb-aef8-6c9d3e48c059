version: '3.8'

services:
  nginx:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: agentfoundry-ui-nginx
    ports:
      - "3000:3000"
    restart: unless-stopped
    depends_on:
      - frontend-vite
      - elsa-service
    networks:
      - app-network

  frontend-vite:
    image: node:18-alpine
    container_name: agentfoundry-ui-vite
    working_dir: /app
    volumes:
      - .:/app:cached
    command: >
      sh -c "yarn install && yarn dev --host 0.0.0.0 --port ${VITE_PORT:-13001}"
    environment:
      VITE_APP_ENVIRONMENT: development
      VITE_API_URL: https://bs-test-llm.ai4c.cn
      HOST: 0.0.0.0
      PORT: ${VITE_PORT:-13001}
    networks:
      - app-network

  elsa-service:
    image: elsaworkflows/elsa-server-and-studio-v3:latest
    container_name: elsa-workflows
    ports:
      - "13000:8080"
    restart: unless-stopped
    environment:
      ELSA__CORS__ALLOWEDORIGINS: http://localhost:3000,http://localhost:13000
      ELSA__CORS__ALLOWEDMETHODS: "*"
      HTTP_PORTS: 8080
      Hosting__BaseUrl: http://localhost:3000
      HTTP__BASEURL: http://localhost:13000
      ASPNETCORE_ENVIRONMENT: Development
    networks:
      - app-network

networks:
  app-network:
    driver: bridge