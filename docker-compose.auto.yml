version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.auto
    container_name: agentfoundry-ui
    ports:
      - "3000:3000"
    # 只需要添加环境变量配置
    environment:
      API_BASE_URL: https://bs-test-llm.ai4c.cn
      # ELSA_BASE_URL: http://elsa-service:8080
    restart: unless-stopped
    networks:
      - app-network
    # depends_on:
    #   - elsa-service

  # elsa-service:
  #   image: elsaworkflows/elsa-server-and-studio-v3:latest
  #   container_name: elsa-workflows
  #   ports:
  #     - "13000:8080"
  #   restart: unless-stopped
  #   networks:
  #     - app-network
  #   environment:
  #     ELSA__CORS__ALLOWEDORIGINS: http://localhost:3000,http://localhost:13000
  #     ELSA__CORS__ALLOWEDMETHODS: '*'
  #     HTTP_PORTS: "8080"
  #     Hosting__BaseUrl: http://localhost:13000
  #     HTTP__BASEURL: http://localhost:13000
  #     ASPNETCORE_ENVIRONMENT: Production
  #     ConnectionStrings__ElsaServer: ${ELSA_CONNECTION_STRING} # 根据实际情况配置

networks:
  app-network:
    driver: bridge